"""
Type-safe plugin configuration system.

This module provides enhanced type safety for plugin configurations,
including automatic validation against plugin schemas and type-safe
configuration access patterns.
"""

from __future__ import annotations

import logging
from typing import Any, TypeVar, cast

from pydantic import BaseModel, ValidationError

from plugginger.core.exceptions import ConfigurationError

# Type variable for plugin configuration schemas
ConfigSchemaT = TypeVar('ConfigSchemaT', bound=BaseModel)


class TypedPluginConfig:
    """
    Type-safe wrapper for plugin configuration.
    
    This class provides type-safe access to plugin configuration data
    with automatic validation against the plugin's declared schema.
    """

    def __init__(
        self,
        plugin_name: str,
        config_data: dict[str, Any],
        config_schema: type[BaseModel] | None = None,
        logger: logging.Logger | None = None
    ) -> None:
        """
        Initialize typed plugin configuration.

        Args:
            plugin_name: Name of the plugin this config belongs to
            config_data: Raw configuration data
            config_schema: Pydantic model for validation (optional)
            logger: Logger for debugging and error reporting
        """
        self._plugin_name = plugin_name
        self._raw_data = config_data.copy()
        self._config_schema = config_schema
        self._logger = logger or logging.getLogger(__name__)
        self._validated_config: BaseModel | None = None

        # Validate configuration if schema is provided
        if config_schema:
            self._validate_config()

    def _validate_config(self) -> None:
        """Validate configuration against the plugin's schema."""
        if not self._config_schema:
            return

        try:
            self._validated_config = self._config_schema(**self._raw_data)
            self._logger.debug(f"Configuration validated for plugin '{self._plugin_name}'")
        except ValidationError as e:
            error_details = []
            for error in e.errors():
                field_path = ".".join(str(loc) for loc in error["loc"])
                error_details.append(f"{field_path}: {error['msg']}")

            error_msg = (
                f"Configuration validation failed for plugin '{self._plugin_name}':\n"
                + "\n".join(error_details)
            )
            self._logger.error(error_msg)
            raise ConfigurationError(error_msg) from e

    def get_typed(self, config_type: type[ConfigSchemaT]) -> ConfigSchemaT:
        """
        Get configuration as a typed Pydantic model.

        Args:
            config_type: Pydantic model class for the configuration

        Returns:
            Validated configuration instance

        Raises:
            ConfigurationError: If validation fails

        Example:
            ```python
            class MyConfig(BaseModel):
                api_key: str
                timeout: int = 30

            config = typed_config.get_typed(MyConfig)
            print(config.api_key)  # Type-safe access
            ```
        """
        try:
            validated = config_type(**self._raw_data)
            self._logger.debug(f"Configuration typed as {config_type.__name__} for plugin '{self._plugin_name}'")
            return validated
        except ValidationError as e:
            error_details = []
            for error in e.errors():
                field_path = ".".join(str(loc) for loc in error["loc"])
                error_details.append(f"{field_path}: {error['msg']}")

            error_msg = (
                f"Configuration typing failed for plugin '{self._plugin_name}' "
                f"with schema {config_type.__name__}:\n"
                + "\n".join(error_details)
            )
            self._logger.error(error_msg)
            raise ConfigurationError(error_msg) from e

    def get_validated(self) -> BaseModel:
        """
        Get the validated configuration if schema was provided.

        Returns:
            Validated configuration instance

        Raises:
            ConfigurationError: If no schema was provided or validation failed
        """
        if not self._config_schema:
            raise ConfigurationError(
                f"No configuration schema provided for plugin '{self._plugin_name}'. "
                "Cannot return validated configuration."
            )

        if self._validated_config is None:
            raise ConfigurationError(
                f"Configuration validation failed for plugin '{self._plugin_name}'. "
                "Check logs for validation errors."
            )

        return self._validated_config

    def get_raw(self) -> dict[str, Any]:
        """
        Get raw configuration data without validation.

        Returns:
            Raw configuration dictionary
        """
        return self._raw_data.copy()

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value by key.

        Args:
            key: Configuration key
            default: Default value if key not found

        Returns:
            Configuration value or default
        """
        return self._raw_data.get(key, default)

    def __getitem__(self, key: str) -> Any:
        """Get configuration value by key (dict-like access)."""
        return self._raw_data[key]

    def __contains__(self, key: str) -> bool:
        """Check if configuration contains a key."""
        return key in self._raw_data

    def keys(self) -> Any:
        """Get configuration keys."""
        return self._raw_data.keys()

    def values(self) -> Any:
        """Get configuration values."""
        return self._raw_data.values()

    def items(self) -> Any:
        """Get configuration items."""
        return self._raw_data.items()

    @property
    def plugin_name(self) -> str:
        """Get the plugin name this configuration belongs to."""
        return self._plugin_name

    @property
    def has_schema(self) -> bool:
        """Check if this configuration has a validation schema."""
        return self._config_schema is not None

    @property
    def is_validated(self) -> bool:
        """Check if this configuration has been successfully validated."""
        return self._validated_config is not None

    def __repr__(self) -> str:
        """String representation of the typed configuration."""
        schema_info = f", schema={self._config_schema.__name__}" if self._config_schema else ""
        validation_info = ", validated=True" if self._validated_config else ""
        return f"TypedPluginConfig(plugin='{self._plugin_name}'{schema_info}{validation_info})"


class ConfigurationManager:
    """
    Manager for type-safe plugin configurations.
    
    This class handles the creation and management of typed plugin
    configurations with automatic validation and type safety.
    """

    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize configuration manager.

        Args:
            logger: Logger for debugging and error reporting
        """
        self._logger = logger or logging.getLogger(__name__)
        self._typed_configs: dict[str, TypedPluginConfig] = {}

    def create_typed_config(
        self,
        plugin_name: str,
        config_data: dict[str, Any],
        config_schema: type[BaseModel] | None = None
    ) -> TypedPluginConfig:
        """
        Create a typed configuration for a plugin.

        Args:
            plugin_name: Name of the plugin
            config_data: Raw configuration data
            config_schema: Optional Pydantic schema for validation

        Returns:
            TypedPluginConfig instance

        Raises:
            ConfigurationError: If validation fails
        """
        self._logger.debug(f"Creating typed configuration for plugin '{plugin_name}'")

        typed_config = TypedPluginConfig(
            plugin_name=plugin_name,
            config_data=config_data,
            config_schema=config_schema,
            logger=self._logger
        )

        self._typed_configs[plugin_name] = typed_config
        return typed_config

    def get_typed_config(self, plugin_name: str) -> TypedPluginConfig | None:
        """
        Get typed configuration for a plugin.

        Args:
            plugin_name: Name of the plugin

        Returns:
            TypedPluginConfig instance or None if not found
        """
        return self._typed_configs.get(plugin_name)

    def validate_all_configs(
        self,
        plugin_configs: dict[str, dict[str, Any]],
        plugin_schemas: dict[str, type[BaseModel] | None]
    ) -> dict[str, TypedPluginConfig]:
        """
        Validate all plugin configurations against their schemas.

        Args:
            plugin_configs: Raw plugin configurations
            plugin_schemas: Plugin configuration schemas

        Returns:
            Dictionary of typed configurations

        Raises:
            ConfigurationError: If any validation fails
        """
        self._logger.info(f"Validating configurations for {len(plugin_configs)} plugins")

        typed_configs: dict[str, TypedPluginConfig] = {}
        validation_errors: list[str] = []

        for plugin_name, config_data in plugin_configs.items():
            try:
                schema = plugin_schemas.get(plugin_name)
                typed_config = self.create_typed_config(
                    plugin_name=plugin_name,
                    config_data=config_data,
                    config_schema=schema
                )
                typed_configs[plugin_name] = typed_config

            except ConfigurationError as e:
                validation_errors.append(str(e))

        if validation_errors:
            error_msg = (
                f"Configuration validation failed for {len(validation_errors)} plugins:\n"
                + "\n".join(validation_errors)
            )
            self._logger.error(error_msg)
            raise ConfigurationError(error_msg)

        self._logger.info(f"Successfully validated configurations for {len(typed_configs)} plugins")
        return typed_configs

    def clear(self) -> None:
        """Clear all typed configurations."""
        self._typed_configs.clear()
        self._logger.debug("Cleared all typed configurations")

    def __len__(self) -> int:
        """Get number of managed configurations."""
        return len(self._typed_configs)

    def __contains__(self, plugin_name: str) -> bool:
        """Check if configuration exists for plugin."""
        return plugin_name in self._typed_configs
