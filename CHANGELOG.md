# Changelog

All notable changes to the Plugginger framework will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- feat(manifest): automatic YAML manifest conversion for user-friendly format (#23)
  - ManifestConverter class for converting user-friendly YAML to Pydantic format
  - Automatic fallback in ManifestLoader for seamless compatibility
  - Support for simplified service, event listener, and dependency definitions
  - Reference App now uses manifests by default with automatic conversion

#### Plugin Manifest System
- **YAML Schema System**: Comprehensive YAML schema for plugin manifests enabling AI agent integration
- **Pydantic Models**: Type-safe manifest models with validation for plugin metadata, services, events, and dependencies
- **Automatic Generation**: Utilities to generate manifests from existing plugin classes with full signature extraction
- **Schema Documentation**: Complete documentation for manifest schema v1.0.0 with examples and best practices

#### Schema Features
- **Plugin Metadata**: Validation for name, version, author, license, keywords with Python identifier constraints
- **Runtime Configuration**: Execution mode, Python/Plugginger version requirements
- **Dependency Declarations**: Version constraints, optional dependencies with SemVer support
- **Service Definitions**: Full method signatures, parameter details, return type annotations
- **Event Listeners**: Pattern matching, priorities, timeout configurations
- **Configuration Schema**: JSON Schema support from Pydantic models for plugin configuration

#### Generator Capabilities
- **Signature Extraction**: Automatic extraction of method signatures with parameter kinds and types
- **Dependency Analysis**: Smart parsing of plugin needs declarations including Depends objects
- **YAML Serialization**: Proper YAML formatting with null handling and Unicode support
- **Validation**: Comprehensive error handling for invalid plugins and malformed data

#### Manifest Validation
- **ManifestValidator**: Comprehensive plugin manifest validation during plugin loading
- **Builder Integration**: `enable_manifest_validation()` and `disable_manifest_validation()` methods
- **Consistency Checks**: Validates plugin metadata, service declarations, event listeners, and dependencies
- **Optional Validation**: Disabled by default with lazy initialization to avoid circular imports
- **Clear Error Messages**: Detailed validation error messages for debugging

#### Manifest Examples
- **Comprehensive Examples**: 11 example manifests covering all plugin types and patterns
- **Plugin Types**: Service-only, event-listener, mixed, dependencies, configuration examples
- **Applications**: Simple app, microservices, AI chat reference application manifests
- **Advanced Patterns**: Fractal plugin, external service, full-featured examples
- **Documentation**: README.md and GUIDE.md with usage instructions and best practices
- **Schema Validation**: All examples validate against current schema with comprehensive tests

#### YAML Manifest Loading
- **ManifestLoader**: Class for loading and validating YAML manifests during plugin registration
- **Builder Integration**: `enable_manifest_loading()` and `disable_manifest_loading()` methods
- **Auto-Discovery**: Common naming conventions (manifest.yaml, {plugin_name}_manifest.yaml)
- **Schema Validation**: Pydantic model validation with detailed error messages
- **Optional/Required Modes**: Flexible enforcement for gradual adoption
- **Error Handling**: Clear error messages with file paths and validation details

#### Discovery Command (AI Agent Integration)
- **CLI Command**: `plugginger inspect --json` for machine-readable app structure analysis
- **AppInspector**: Comprehensive application analysis class for extracting plugin metadata
- **Service Signatures**: Detailed method signature extraction with parameter types, defaults, and docstrings
- **Event Listeners**: Complete event listener analysis with patterns and metadata
- **Dependency Graph**: Plugin dependency visualization with cycle detection and version constraints
- **JSON Schema**: Standardized JSON format for app graph with comprehensive validation
- **AI Agent Support**: Enables autonomous plugin development and integration by AI agents

#### Structured Logging System
- **StructuredLogger**: AI-Agent-friendly JSON logging with rich context and performance metrics
- **Event Types**: Comprehensive event type enumeration for build, plugin, DI, and performance events
- **Performance Timing**: Automatic operation timing with OperationTimer context manager
- **Build Integration**: Full integration with PluggingerAppBuilder for build event tracking
- **LogAnalyzer**: AI-Agent analysis tools for performance optimization and issue detection
- **Correlation IDs**: Session-based correlation tracking for log grouping and analysis
- **Configurable Output**: Enable/disable structured logging with fallback to traditional logging

## [0.9.0-alpha] - 2025-01-15

### Added

#### Breaking Change Policy & API Stability
- **Breaking Change Policy**: Comprehensive policy document defining what constitutes breaking changes and API stability levels
- **Deprecation Utilities**: New `@deprecated` decorator with removal version tracking and replacement suggestions
- **Stability Markers**: API stability decorators (`@experimental`, `@stable_candidate`, `@stable`) with automatic warnings
- **Policy Enforcement**: Utilities for checking API compatibility and managing transitions

#### Core API Documentation
- **Complete API Documentation**: Comprehensive documentation for all stable candidate APIs
- **Plugin API Guide**: Full documentation for `@plugin`, `PluginBase`, and plugin patterns
- **Service API Guide**: Complete guide for `@service` decorator and service calling patterns
- **Events API Guide**: Detailed documentation for `@on_event` and event-driven architecture
- **Quick Start Guide**: Step-by-step guide with practical examples and common patterns
- **Migration Guide**: Instructions for migrating from experimental to stable APIs

#### Experimental Namespace
- **Experimental Package**: New `plugginger.experimental.*` namespace for unstable APIs
- **Advanced Features**: Experimental implementations of fractal composition, event sourcing, and plugin registry
- **Warning System**: Automatic `FutureWarning` when importing experimental features
- **Lazy Loading**: Dynamic imports to reduce startup time and avoid dependency issues

#### Framework Infrastructure
- **Version Bump**: Updated to v0.9.0-alpha marking transition to stable candidate APIs
- **Test Coverage**: Comprehensive test suite with 851+ tests and 76%+ coverage
- **Code Quality**: Full compliance with ruff and mypy --strict requirements

### Changed

#### API Stability
- **Stable Candidate Status**: Core APIs (`plugginger.api.*`) marked as stable candidates
- **Backward Compatibility**: Established backward compatibility guarantees for stable APIs
- **Breaking Change Protection**: Clear policies preventing uncontrolled breaking changes

#### Documentation Structure
- **Organized Documentation**: Restructured docs with clear separation of stable vs experimental features
- **API Reference**: Complete API reference with examples and best practices
- **README Updates**: Enhanced README with API stability information and policy references

### Technical Details

#### New Modules
- `src/plugginger/core/deprecation.py` - Deprecation utilities and decorators
- `src/plugginger/core/stability.py` - API stability markers and compatibility checking
- `src/plugginger/experimental/` - Experimental features namespace
- `docs/BREAKING_CHANGE_POLICY.md` - Comprehensive breaking change policy
- `docs/core-api/` - Complete stable API documentation

#### Test Coverage
- 17 new tests for breaking change policy enforcement
- 12 new tests for core API documentation completeness
- 12 new tests for experimental namespace functionality
- All tests passing with 76.27% overall coverage

#### Code Quality
- Full ruff compliance (0 errors)
- Full mypy --strict compliance (0 errors)
- Comprehensive type hints throughout new modules
- Detailed docstrings with examples

### Migration Guide

#### For Plugin Developers
1. **Use Stable APIs**: Migrate from any experimental APIs to `plugginger.api.*`
2. **Update Imports**: Replace experimental imports with stable equivalents
3. **Follow New Patterns**: Use documented patterns from the API guides

#### For Framework Contributors
1. **Follow Policy**: All changes must comply with the breaking change policy
2. **Use Decorators**: Mark new APIs with appropriate stability decorators
3. **Write Tests**: Ensure comprehensive test coverage for all changes

### Notes

This release establishes the foundation for v1.0.0 by:
- Defining clear API stability levels and policies
- Providing comprehensive documentation for stable APIs
- Implementing tools for managing API transitions
- Establishing backward compatibility guarantees

The framework is now ready for production use with stable candidate APIs while maintaining
flexibility for experimental features in the dedicated experimental namespace.

---

## Version History

- **v0.9.0-alpha** (2025-01-15): API stability and comprehensive documentation
- **v0.8.x**: Previous development versions (pre-stability policy)

## Links

- [Breaking Change Policy](docs/BREAKING_CHANGE_POLICY.md)
- [Core API Documentation](docs/core-api/README.md)
- [GitHub Repository](https://github.com/jkehrhahn/plugginger)
- [Issues & Feedback](https://github.com/jkehrhahn/plugginger/issues)