"""
Unit tests for TypedPluginConfig and ConfigurationManager.

Tests the type-safe plugin configuration system including validation,
type safety, and error handling.
"""

from __future__ import annotations

from typing import Any

import pytest
from pydantic import BaseModel, Field

from plugginger.config.typed_config import Configuration<PERSON>ana<PERSON>, TypedPluginConfig
from plugginger.core.exceptions import ConfigurationError


class TestConfig(BaseModel):
    """Test configuration schema for testing."""
    
    api_key: str
    timeout: int = 30
    debug: bool = False
    tags: list[str] = Field(default_factory=list)


class DatabaseConfig(BaseModel):
    """Database configuration schema for testing."""
    
    host: str
    port: int = 5432
    username: str
    password: str
    ssl_enabled: bool = True


class TestTypedPluginConfig:
    """Test cases for TypedPluginConfig class."""

    def test_init_without_schema(self) -> None:
        """Test initialization without validation schema."""
        config_data = {"key": "value", "number": 42}
        
        typed_config = TypedPluginConfig(
            plugin_name="test_plugin",
            config_data=config_data
        )
        
        assert typed_config.plugin_name == "test_plugin"
        assert typed_config.get_raw() == config_data
        assert not typed_config.has_schema
        assert not typed_config.is_validated

    def test_init_with_valid_schema(self) -> None:
        """Test initialization with valid schema and data."""
        config_data = {
            "api_key": "secret123",
            "timeout": 60,
            "debug": True,
            "tags": ["test", "api"]
        }
        
        typed_config = TypedPluginConfig(
            plugin_name="test_plugin",
            config_data=config_data,
            config_schema=TestConfig
        )
        
        assert typed_config.plugin_name == "test_plugin"
        assert typed_config.has_schema
        assert typed_config.is_validated
        
        validated = typed_config.get_validated()
        assert isinstance(validated, TestConfig)
        assert validated.api_key == "secret123"
        assert validated.timeout == 60
        assert validated.debug is True
        assert validated.tags == ["test", "api"]

    def test_init_with_invalid_schema(self) -> None:
        """Test initialization with invalid data against schema."""
        config_data = {
            "timeout": "not_a_number",  # Should be int
            "debug": "not_a_bool"       # Should be bool
        }
        
        with pytest.raises(ConfigurationError) as exc_info:
            TypedPluginConfig(
                plugin_name="test_plugin",
                config_data=config_data,
                config_schema=TestConfig
            )
        
        error_msg = str(exc_info.value)
        assert "Configuration validation failed" in error_msg
        assert "test_plugin" in error_msg

    def test_get_typed_valid(self) -> None:
        """Test get_typed with valid configuration."""
        config_data = {
            "host": "localhost",
            "port": 5432,
            "username": "user",
            "password": "secret"
        }
        
        typed_config = TypedPluginConfig(
            plugin_name="db_plugin",
            config_data=config_data
        )
        
        db_config = typed_config.get_typed(DatabaseConfig)
        assert isinstance(db_config, DatabaseConfig)
        assert db_config.host == "localhost"
        assert db_config.port == 5432
        assert db_config.username == "user"
        assert db_config.password == "secret"
        assert db_config.ssl_enabled is True  # Default value

    def test_get_typed_invalid(self) -> None:
        """Test get_typed with invalid configuration."""
        config_data = {
            "host": "localhost",
            "port": "not_a_number",  # Should be int
            "username": "user"
            # Missing required 'password' field
        }
        
        typed_config = TypedPluginConfig(
            plugin_name="db_plugin",
            config_data=config_data
        )
        
        with pytest.raises(ConfigurationError) as exc_info:
            typed_config.get_typed(DatabaseConfig)
        
        error_msg = str(exc_info.value)
        assert "Configuration typing failed" in error_msg
        assert "db_plugin" in error_msg
        assert "DatabaseConfig" in error_msg

    def test_get_validated_without_schema(self) -> None:
        """Test get_validated when no schema was provided."""
        typed_config = TypedPluginConfig(
            plugin_name="test_plugin",
            config_data={"key": "value"}
        )
        
        with pytest.raises(ConfigurationError) as exc_info:
            typed_config.get_validated()
        
        error_msg = str(exc_info.value)
        assert "No configuration schema provided" in error_msg
        assert "test_plugin" in error_msg

    def test_dict_like_access(self) -> None:
        """Test dictionary-like access methods."""
        config_data = {
            "api_key": "secret123",
            "timeout": 60,
            "debug": True
        }
        
        typed_config = TypedPluginConfig(
            plugin_name="test_plugin",
            config_data=config_data
        )
        
        # Test get method
        assert typed_config.get("api_key") == "secret123"
        assert typed_config.get("nonexistent", "default") == "default"
        
        # Test __getitem__
        assert typed_config["timeout"] == 60
        
        # Test __contains__
        assert "debug" in typed_config
        assert "nonexistent" not in typed_config
        
        # Test keys, values, items
        assert set(typed_config.keys()) == {"api_key", "timeout", "debug"}
        assert 60 in typed_config.values()
        assert ("debug", True) in typed_config.items()

    def test_repr(self) -> None:
        """Test string representation."""
        typed_config = TypedPluginConfig(
            plugin_name="test_plugin",
            config_data={"key": "value"},
            config_schema=TestConfig
        )
        
        repr_str = repr(typed_config)
        assert "TypedPluginConfig" in repr_str
        assert "test_plugin" in repr_str
        assert "TestConfig" in repr_str


class TestConfigurationManager:
    """Test cases for ConfigurationManager class."""

    def test_create_typed_config(self) -> None:
        """Test creating typed configuration."""
        manager = ConfigurationManager()
        
        config_data = {"api_key": "secret123", "timeout": 60}
        
        typed_config = manager.create_typed_config(
            plugin_name="test_plugin",
            config_data=config_data,
            config_schema=TestConfig
        )
        
        assert isinstance(typed_config, TypedPluginConfig)
        assert typed_config.plugin_name == "test_plugin"
        assert typed_config.has_schema
        assert typed_config.is_validated

    def test_get_typed_config(self) -> None:
        """Test retrieving typed configuration."""
        manager = ConfigurationManager()
        
        # Create a config
        config_data = {"api_key": "secret123"}
        typed_config = manager.create_typed_config(
            plugin_name="test_plugin",
            config_data=config_data
        )
        
        # Retrieve it
        retrieved = manager.get_typed_config("test_plugin")
        assert retrieved is typed_config
        
        # Non-existent config
        assert manager.get_typed_config("nonexistent") is None

    def test_validate_all_configs_success(self) -> None:
        """Test validating all configurations successfully."""
        manager = ConfigurationManager()
        
        plugin_configs = {
            "api_plugin": {"api_key": "secret123", "timeout": 60},
            "db_plugin": {"host": "localhost", "username": "user", "password": "secret"}
        }
        
        plugin_schemas = {
            "api_plugin": TestConfig,
            "db_plugin": DatabaseConfig
        }
        
        typed_configs = manager.validate_all_configs(plugin_configs, plugin_schemas)
        
        assert len(typed_configs) == 2
        assert "api_plugin" in typed_configs
        assert "db_plugin" in typed_configs
        
        # Check that configs are properly typed
        api_config = typed_configs["api_plugin"]
        assert api_config.plugin_name == "api_plugin"
        assert api_config.has_schema
        assert api_config.is_validated
        
        db_config = typed_configs["db_plugin"]
        assert db_config.plugin_name == "db_plugin"
        assert db_config.has_schema
        assert db_config.is_validated

    def test_validate_all_configs_with_errors(self) -> None:
        """Test validating configurations with validation errors."""
        manager = ConfigurationManager()
        
        plugin_configs = {
            "valid_plugin": {"api_key": "secret123"},
            "invalid_plugin": {"timeout": "not_a_number"}  # Invalid type
        }
        
        plugin_schemas = {
            "valid_plugin": TestConfig,
            "invalid_plugin": TestConfig
        }
        
        with pytest.raises(ConfigurationError) as exc_info:
            manager.validate_all_configs(plugin_configs, plugin_schemas)
        
        error_msg = str(exc_info.value)
        assert "Configuration validation failed" in error_msg
        assert "invalid_plugin" in error_msg

    def test_validate_all_configs_no_schema(self) -> None:
        """Test validating configurations without schemas."""
        manager = ConfigurationManager()
        
        plugin_configs = {
            "plugin1": {"key": "value1"},
            "plugin2": {"key": "value2"}
        }
        
        plugin_schemas = {
            "plugin1": None,
            "plugin2": None
        }
        
        typed_configs = manager.validate_all_configs(plugin_configs, plugin_schemas)
        
        assert len(typed_configs) == 2
        for plugin_id, typed_config in typed_configs.items():
            assert not typed_config.has_schema
            assert not typed_config.is_validated

    def test_clear(self) -> None:
        """Test clearing all configurations."""
        manager = ConfigurationManager()
        
        # Add some configs
        manager.create_typed_config("plugin1", {"key": "value1"})
        manager.create_typed_config("plugin2", {"key": "value2"})
        
        assert len(manager) == 2
        assert "plugin1" in manager
        assert "plugin2" in manager
        
        # Clear all
        manager.clear()
        
        assert len(manager) == 0
        assert "plugin1" not in manager
        assert "plugin2" not in manager

    def test_len_and_contains(self) -> None:
        """Test length and contains operations."""
        manager = ConfigurationManager()
        
        assert len(manager) == 0
        assert "plugin1" not in manager
        
        manager.create_typed_config("plugin1", {"key": "value1"})
        
        assert len(manager) == 1
        assert "plugin1" in manager
        assert "plugin2" not in manager
