"""
Integration tests for PluggingerAppBuilder config type safety.

Tests the integration between PluggingerAppBuilder and the type-safe
configuration system, ensuring that typed configurations work properly
with the full application lifecycle.
"""

from __future__ import annotations

import pytest
from pydantic import BaseModel, Field

from plugginger.api.builder import Plugginger<PERSON><PERSON><PERSON>uilder
from plugginger.api.plugin import PluginB<PERSON>, plugin
from plugginger.api.service import service
from plugginger.config.typed_config import TypedPluginConfig
from plugginger.core.exceptions import ConfigurationError


class DatabaseConfig(BaseModel):
    """Database configuration schema for testing."""
    
    host: str
    port: int = 5432
    username: str
    password: str
    ssl_enabled: bool = True
    max_connections: int = Field(default=10, ge=1, le=100)


class APIConfig(BaseModel):
    """API configuration schema for testing."""
    
    api_key: str
    base_url: str = "https://api.example.com"
    timeout: int = 30
    retry_count: int = Field(default=3, ge=0, le=10)


@plugin(name="database_plugin", version="1.0.0")
class DatabasePlugin(PluginBase):
    """Test database plugin with configuration schema."""
    
    # Define configuration schema
    config_schema = DatabaseConfig
    
    @service()
    async def connect(self) -> str:
        """Connect to database."""
        return f"Connected to database"

    @service()
    async def get_connection_info(self) -> dict[str, str]:
        """Get connection information."""
        return {"status": "connected", "type": "database"}


@plugin(name="api_plugin", version="1.0.0")
class APIPlugin(PluginBase):
    """Test API plugin with configuration schema."""
    
    # Define configuration schema
    config_schema = APIConfig
    
    @service()
    async def make_request(self, endpoint: str) -> str:
        """Make API request."""
        return f"Request to {endpoint}"


@plugin(name="simple_plugin", version="1.0.0")
class SimplePlugin(PluginBase):
    """Test plugin without configuration schema."""
    
    @service()
    async def simple_service(self) -> str:
        """Simple service."""
        return "simple result"


class TestBuilderConfigIntegration:
    """Integration test cases for builder config type safety."""

    def test_configure_plugin_typed_basic(self) -> None:
        """Test basic typed plugin configuration."""
        builder = PluggingerAppBuilder("test_app")
        
        config_data = {
            "host": "localhost",
            "port": 5432,
            "username": "testuser",
            "password": "testpass",
            "ssl_enabled": False,
            "max_connections": 20
        }
        
        typed_config = builder.configure_plugin_typed(
            plugin_id="database_plugin",
            config=config_data,
            config_schema=DatabaseConfig
        )
        
        assert isinstance(typed_config, TypedPluginConfig)
        assert typed_config.plugin_name == "database_plugin"
        assert typed_config.has_schema
        assert typed_config.is_validated
        
        # Test type-safe access
        db_config = typed_config.get_typed(DatabaseConfig)
        assert db_config.host == "localhost"
        assert db_config.port == 5432
        assert db_config.username == "testuser"
        assert db_config.password == "testpass"
        assert db_config.ssl_enabled is False
        assert db_config.max_connections == 20

    def test_configure_plugin_typed_validation_error(self) -> None:
        """Test typed configuration with validation errors."""
        builder = PluggingerAppBuilder("test_app")
        
        invalid_config = {
            "host": "localhost",
            "port": "not_a_number",  # Should be int
            "username": "testuser"
            # Missing required 'password' field
        }
        
        with pytest.raises(ConfigurationError) as exc_info:
            builder.configure_plugin_typed(
                plugin_id="database_plugin",
                config=invalid_config,
                config_schema=DatabaseConfig
            )
        
        error_msg = str(exc_info.value)
        assert "Configuration validation failed" in error_msg
        assert "database_plugin" in error_msg

    def test_configure_plugin_typed_without_schema(self) -> None:
        """Test typed configuration without validation schema."""
        builder = PluggingerAppBuilder("test_app")
        
        config_data = {"arbitrary_key": "arbitrary_value", "number": 42}
        
        typed_config = builder.configure_plugin_typed(
            plugin_id="simple_plugin",
            config=config_data
        )
        
        assert isinstance(typed_config, TypedPluginConfig)
        assert typed_config.plugin_name == "simple_plugin"
        assert not typed_config.has_schema
        assert not typed_config.is_validated
        
        # Raw access should work
        assert typed_config.get_raw() == config_data
        assert typed_config["arbitrary_key"] == "arbitrary_value"
        assert typed_config["number"] == 42

    def test_get_plugin_config_typed(self) -> None:
        """Test retrieving typed plugin configuration."""
        builder = PluggingerAppBuilder("test_app")
        
        # Configure a plugin
        config_data = {"api_key": "secret123", "timeout": 60}
        builder.configure_plugin_typed(
            plugin_id="api_plugin",
            config=config_data,
            config_schema=APIConfig
        )
        
        # Retrieve the configuration
        typed_config = builder.get_plugin_config_typed("api_plugin")
        assert typed_config is not None
        assert typed_config.plugin_name == "api_plugin"
        assert typed_config.has_schema
        assert typed_config.is_validated
        
        # Non-existent plugin
        assert builder.get_plugin_config_typed("nonexistent") is None

    def test_validate_all_plugin_configs_with_schemas(self) -> None:
        """Test validating all plugin configurations with explicit schemas."""
        builder = PluggingerAppBuilder("test_app")
        
        # Configure multiple plugins
        builder.configure_plugin_typed(
            plugin_id="database_plugin",
            config={"host": "localhost", "username": "user", "password": "pass"}
        )
        
        builder.configure_plugin_typed(
            plugin_id="api_plugin",
            config={"api_key": "secret123"}
        )
        
        # Validate with explicit schemas
        plugin_schemas = {
            "database_plugin": DatabaseConfig,
            "api_plugin": APIConfig
        }
        
        typed_configs = builder.validate_all_plugin_configs(plugin_schemas)
        
        assert len(typed_configs) == 2
        assert "database_plugin" in typed_configs
        assert "api_plugin" in typed_configs
        
        # Check database config
        db_config = typed_configs["database_plugin"]
        assert db_config.has_schema
        assert db_config.is_validated
        db_typed = db_config.get_typed(DatabaseConfig)
        assert db_typed.host == "localhost"
        assert db_typed.port == 5432  # Default value
        
        # Check API config
        api_config = typed_configs["api_plugin"]
        assert api_config.has_schema
        assert api_config.is_validated
        api_typed = api_config.get_typed(APIConfig)
        assert api_typed.api_key == "secret123"
        assert api_typed.base_url == "https://api.example.com"  # Default value

    def test_validate_all_plugin_configs_auto_extract_schemas(self) -> None:
        """Test validating configurations with auto-extracted schemas."""
        builder = PluggingerAppBuilder("test_app")
        
        # Include plugins with schemas
        builder.include(DatabasePlugin)
        builder.include(APIPlugin)
        builder.include(SimplePlugin)
        
        # Configure plugins
        builder.configure_plugin_typed(
            plugin_id="database_plugin",
            config={"host": "localhost", "username": "user", "password": "pass"}
        )
        
        builder.configure_plugin_typed(
            plugin_id="api_plugin",
            config={"api_key": "secret123"}
        )
        
        builder.configure_plugin_typed(
            plugin_id="simple_plugin",
            config={"simple_key": "simple_value"}
        )
        
        # Validate without explicit schemas (should auto-extract)
        typed_configs = builder.validate_all_plugin_configs()
        
        assert len(typed_configs) == 3
        
        # Database plugin should have schema
        db_config = typed_configs["database_plugin"]
        assert db_config.has_schema
        assert db_config.is_validated
        
        # API plugin should have schema
        api_config = typed_configs["api_plugin"]
        assert api_config.has_schema
        assert api_config.is_validated
        
        # Simple plugin should not have schema
        simple_config = typed_configs["simple_plugin"]
        assert not simple_config.has_schema
        assert not simple_config.is_validated

    def test_validate_all_plugin_configs_with_errors(self) -> None:
        """Test validation with configuration errors."""
        builder = PluggingerAppBuilder("test_app")
        
        # Configure with invalid data
        builder.configure_plugin_typed(
            plugin_id="database_plugin",
            config={"host": "localhost", "port": "invalid_port"}  # Invalid type
        )
        
        plugin_schemas = {"database_plugin": DatabaseConfig}
        
        with pytest.raises(ConfigurationError) as exc_info:
            builder.validate_all_plugin_configs(plugin_schemas)
        
        error_msg = str(exc_info.value)
        assert "Configuration validation failed" in error_msg

    @pytest.mark.asyncio
    async def test_full_integration_with_typed_config(self) -> None:
        """Test full integration: configure, build, and run with typed configs."""
        builder = PluggingerAppBuilder("test_app")
        
        # Include plugins
        builder.include(DatabasePlugin)
        builder.include(APIPlugin)
        
        # Configure with typed configs
        builder.configure_plugin_typed(
            plugin_id="database_plugin",
            config={
                "host": "localhost",
                "port": 5432,
                "username": "testuser",
                "password": "testpass",
                "max_connections": 15
            },
            config_schema=DatabaseConfig
        )
        
        builder.configure_plugin_typed(
            plugin_id="api_plugin",
            config={
                "api_key": "secret123",
                "base_url": "https://test.api.com",
                "timeout": 45
            },
            config_schema=APIConfig
        )
        
        # Build the application
        app = builder.build()
        
        # Start plugins
        await app.start_all_plugins()
        
        try:
            # Test that services work
            db_result = await app.call_service("database_plugin.connect")
            assert "Connected to database" in db_result
            
            api_result = await app.call_service("api_plugin.make_request", endpoint="/test")
            assert "Request to /test" in api_result
            
            # Verify services are available
            services = app.list_services()
            assert "database_plugin.connect" in services
            assert "database_plugin.get_connection_info" in services
            assert "api_plugin.make_request" in services
            
        finally:
            await app.stop_all_plugins()

    def test_config_persistence_through_build(self) -> None:
        """Test that typed configurations persist through the build process."""
        builder = PluggingerAppBuilder("test_app")
        
        # Configure plugin
        config_data = {
            "host": "localhost",
            "username": "user",
            "password": "pass",
            "max_connections": 25
        }
        
        typed_config = builder.configure_plugin_typed(
            plugin_id="database_plugin",
            config=config_data,
            config_schema=DatabaseConfig
        )
        
        # Include plugin
        builder.include(DatabasePlugin)
        
        # Build app
        app = builder.build()
        
        # Verify configuration is available in the built app
        # Note: This tests that the config was properly transferred to GlobalAppConfig
        assert app is not None
        
        # The typed config should still be accessible from the builder
        retrieved_config = builder.get_plugin_config_typed("database_plugin")
        assert retrieved_config is not None
        assert retrieved_config.plugin_name == "database_plugin"
        
        # Verify the data is the same
        db_config = retrieved_config.get_typed(DatabaseConfig)
        assert db_config.host == "localhost"
        assert db_config.username == "user"
        assert db_config.password == "pass"
        assert db_config.max_connections == 25
